@import '../../global';

// Header
.mainContainer {
    width: max-content; // Fixed width for vertical sidebar
    height: 100vh;
    padding: 20px 0;
    box-sizing: border-box;
    background-color: $primaryColor;
    display: flex;
    flex-direction: column;
    flex-shrink: 0; // Prevent shrinking

    // Logo + Account
    .logoAccountContainer {
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        margin-bottom: 30px;

        .logo {
            width: 60px; // Smaller for vertical layout
            height: auto;
            box-sizing: border-box;
        }

        // Checkout + Login/Register (used in 2 places)
        .checkoutAccountContainer {
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 15px;

            // Login/Register
            .loginRegisterContainer {
                width: max-content;
                height: max-content;
                cursor: pointer;

                .loginRegisterText {
                    padding: 8px 0;
                    font-size: 18px;
                    font-weight: bold;
                    text-align: center;
                }
                &:hover {
                    color: $primaryColor;
                }
            }

            &.tinyCheckoutAccount {
                display: flex; // Show in vertical layout
            }
        }
    }

    // Navs
    .navsContainer {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;

        // Nav Buttons
        .navButtons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            align-items: center;
        }
    }

    // Custom Nav Button Styles
    .navButton {
        width: 100%;
        max-width: 120px;
        padding: 12px 8px;
        background-color: transparent;
        border: 1px solid transparent;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
        box-sizing: border-box;

        &:hover {
            background-color: rgba(0, 0, 0, 0.05);
            border-color: $primaryColor;
        }

        .navButtonContent {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            .navIcon {
                width: 24px;
                height: 24px;
                opacity: 0.9;
                transition: opacity 0.3s ease;
                filter: brightness(0) saturate(100%) invert(58%) sepia(36%) saturate(1234%) hue-rotate(169deg) brightness(95%) contrast(85%);
            }

            .navLabel {
                font-size: 14px;
                font-weight: 500;
                text-align: center;
                line-height: 1.2;
                color: $secondaryColor;
            }
        }

        &:hover .navButtonContent .navIcon {
            opacity: 1;
        }

        // Active state when on current page
        &.active {
            background-color: $secondaryColor;
            cursor: default;
            pointer-events: none;

            &:hover {
                background-color: $secondaryColor;
                border-color: transparent;
                transform: none;
            }

            .navButtonContent {
                .navIcon {
                    opacity: 1;
                    filter: brightness(0) saturate(100%) invert(100%); // White color
                }

                .navLabel {
                    color: $white;
                }
            }
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .mainContainer {
        width: 250px; // Slightly narrower on medium screens

        .logoAccountContainer {
            .logo {
                width: 150px;
            }
        }
    }
}

@media (max-width: $smallScreenSize) {
    .mainContainer {
        width: 200px; // Even narrower on small screens
        padding: 15px;

        .logoAccountContainer {
            .logo {
                width: 120px;
            }
            .checkoutAccountContainer {
                .loginRegisterText {
                    font-size: 16px;
                }
            }
        }

        .navButton {
            max-width: 100px;
            padding: 10px 6px;

            .navButtonContent {
                .navIcon {
                    width: 20px;
                    height: 20px;
                }
                .navLabel {
                    font-size: 12px;
                }
            }
        }
    }
}

@media (max-width: $tinyScreenSize) {
    .mainContainer {
        width: 180px; // Very narrow on tiny screens
        padding: 10px;

        .logoAccountContainer {
            .logo {
                width: 100px;
            }
            .checkoutAccountContainer {
                .loginRegisterText {
                    font-size: 14px;
                }
            }
        }

        .navButton {
            max-width: 80px;
            padding: 8px 4px;

            .navButtonContent {
                .navIcon {
                    width: 18px;
                    height: 18px;
                }
                .navLabel {
                    font-size: 11px;
                }
            }
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        width: 160px; // Minimal width for very small screens
        padding: 8px;

        .logoAccountContainer {
            .logo {
                width: 80px;
            }
            .checkoutAccountContainer {
                .loginRegisterText {
                    font-size: 12px;
                }
            }
        }

        .navsContainer {
            .navButtons {
                gap: 8px;
            }
        }

        .navButton {
            max-width: 70px;
            padding: 6px 3px;

            .navButtonContent {
                gap: 6px;

                .navIcon {
                    width: 16px;
                    height: 16px;
                }
                .navLabel {
                    font-size: 10px;
                }
            }
        }
    }
}