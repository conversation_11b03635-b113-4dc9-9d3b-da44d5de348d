"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Dropdown from "../components/JC_Dropdown/JC_Dropdown";
import { PropertyModel } from "../models/Property";
import { O_BuildingTypeModel } from "../models/O_BuildingType";
import { O_OrientationModel } from "../models/O_Orientation";
import { O_NumBedroomsModel } from "../models/O_NumBedrooms";
import { O_StoreysModel } from "../models/O_Storeys";
import { O_FurnishedModel } from "../models/O_Furnished";
import { O_OccupiedModel } from "../models/O_Occupied";
import { O_RoomModel } from "../models/O_Room";
import { FieldTypeEnum } from "../enums/FieldType";
import { DropdownTypeEnum } from "../enums/DropdownType";
import { JC_Utils, JC_Utils_Rooms } from "../Utils";
import { JC_FieldOption } from "../models/ComponentModels/JC_FieldOption";

export default function PropertyPage() {
    // - STATE - //
    const [currentProperty, setCurrentProperty] = useState<PropertyModel>(new PropertyModel());
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [rerenderGuid, setRerenderGuid] = useState<string>(JC_Utils.generateGuid());

    // Option lists
    const [buildingTypeOptions, setBuildingTypeOptions] = useState<JC_FieldOption[]>([]);
    const [orientationOptions, setOrientationOptions] = useState<JC_FieldOption[]>([]);
    const [numBedroomsOptions, setNumBedroomsOptions] = useState<JC_FieldOption[]>([]);
    const [storeysOptions, setStoreysOptions] = useState<JC_FieldOption[]>([]);
    const [furnishedOptions, setFurnishedOptions] = useState<JC_FieldOption[]>([]);
    const [occupiedOptions, setOccupiedOptions] = useState<JC_FieldOption[]>([]);
    const [roomOptions, setRoomOptions] = useState<O_RoomModel[]>([]);
    const [roomFieldOptions, setRoomFieldOptions] = useState<JC_FieldOption[]>([]);

    // Form fields
    const [address, setAddress] = useState<string>("");
    const [buildingTypeCode, setBuildingTypeCode] = useState<string>("");
    const [companyStrataTitle, setCompanyStrataTitle] = useState<string>("");
    const [numBedroomsCode, setNumBedroomsCode] = useState<string>("");
    const [orientationCode, setOrientationCode] = useState<string>("");
    const [storeysCode, setStoreysCode] = useState<string>("");
    const [furnishedCode, setFurnishedCode] = useState<string>("");
    const [occupiedCode, setOccupiedCode] = useState<string>("");
    const [selectedRoomCodes, setSelectedRoomCodes] = useState<string[]>([]);

    // - EFFECTS - //
    const populateFormFromProperty = useCallback((property: PropertyModel) => {
        setAddress(property.Address || "");
        setBuildingTypeCode(property.BuildingTypeCode || "");
        setCompanyStrataTitle(property.CompanyStrataTitleCode || "");
        setNumBedroomsCode(property.NumBedroomsCode || "");
        setOrientationCode(property.OrientationCode || "");
        setStoreysCode(property.StoreysCode || "");
        setFurnishedCode(property.FurnishedCode || "");
        setOccupiedCode(property.OccupiedCode || "");

        // Parse rooms from JSON
        const roomCodes = JC_Utils_Rooms.parseRoomsJson(property.RoomsListJson);
        setSelectedRoomCodes(roomCodes);
    }, []);

    const loadProperty = useCallback(async () => {
        // For now, create a new property. In a real app, you might load an existing one
        const newProperty = new PropertyModel();
        setCurrentProperty(newProperty);
        populateFormFromProperty(newProperty);
    }, [populateFormFromProperty]);

    const updateRoomFieldOptions = useCallback(() => {
        const options = JC_Utils_Rooms.convertToFieldOptions(roomOptions).map(option => ({
            ...option,
            Selected: selectedRoomCodes.includes(option.OptionId)
        }));
        setRoomFieldOptions(options);
    }, [roomOptions, selectedRoomCodes]);

    useEffect(() => {
        loadOptions();
        loadProperty();
    }, [loadProperty]);

    useEffect(() => {
        updateRoomFieldOptions();
    }, [updateRoomFieldOptions]);

    // - LOAD DATA - //
    async function loadOptions() {
        try {
            setIsLoading(true);

            // Load all option lists
            const [
                buildingTypes,
                orientations,
                numBedrooms,
                storeys,
                furnished,
                occupied,
                rooms
            ] = await Promise.all([
                O_BuildingTypeModel.GetList(),
                O_OrientationModel.GetList(),
                O_NumBedroomsModel.GetList(),
                O_StoreysModel.GetList(),
                O_FurnishedModel.GetList(),
                O_OccupiedModel.GetList(),
                O_RoomModel.GetList()
            ]);

            // Convert to field options
            setBuildingTypeOptions(JC_Utils_Rooms.convertToFieldOptions(buildingTypes));
            setOrientationOptions(JC_Utils_Rooms.convertToFieldOptions(orientations));
            setNumBedroomsOptions(JC_Utils_Rooms.convertToFieldOptions(numBedrooms));
            setStoreysOptions(JC_Utils_Rooms.convertToFieldOptions(storeys));
            setFurnishedOptions(JC_Utils_Rooms.convertToFieldOptions(furnished));
            setOccupiedOptions(JC_Utils_Rooms.convertToFieldOptions(occupied));
            setRoomOptions(rooms);

        } catch (error) {
            console.error('Error loading options:', error);
        } finally {
            setIsLoading(false);
        }
    }



    // - FORM HANDLING - //
    function handleRoomSelection(roomCode: string) {
        const newSelectedCodes = selectedRoomCodes.includes(roomCode)
            ? selectedRoomCodes.filter(code => code !== roomCode)
            : [...selectedRoomCodes, roomCode];

        setSelectedRoomCodes(newSelectedCodes);
    }

    function removeRoom(roomCode: string) {
        setSelectedRoomCodes(selectedRoomCodes.filter(code => code !== roomCode));
    }

    async function handleSubmit() {
        try {
            setIsLoading(true);

            const updatedProperty = new PropertyModel({
                ...currentProperty,
                Address: address,
                BuildingTypeCode: buildingTypeCode || undefined,
                CompanyStrataTitleCode: companyStrataTitle || undefined,
                NumBedroomsCode: numBedroomsCode || undefined,
                OrientationCode: orientationCode || undefined,
                StoreysCode: storeysCode || undefined,
                FurnishedCode: furnishedCode || undefined,
                OccupiedCode: occupiedCode || undefined,
                RoomsListJson: JC_Utils_Rooms.stringifyRoomsJson(selectedRoomCodes)
            });

            let response;
            if (JC_Utils.stringNullOrEmpty(currentProperty.Id)) {
                // Create new property
                response = await PropertyModel.Create(updatedProperty);
            } else {
                // Update existing property
                response = await PropertyModel.Update(updatedProperty);
            }

            if (response) {
                setCurrentProperty(updatedProperty);
                setRerenderGuid(JC_Utils.generateGuid());
            }
        } catch (error) {
            console.error('Error saving property:', error);
        } finally {
            setIsLoading(false);
        }
    }

    // - COMPUTED VALUES - //
    const selectedRoomNames = JC_Utils_Rooms.getSelectedRoomNames(selectedRoomCodes, roomOptions);

    // - RENDER - //
    return (
        <div className={styles.mainContainer}>
            {/* Left Pane - Property Form */}
            <div className={styles.leftPane}>
                <div className={styles.leftPaneHeader}>
                    Property Details
                </div>
                <div className={styles.formContainer}>
                    <div className={styles.formTitle}>
                        {JC_Utils.stringNullOrEmpty(currentProperty.Id) ? "New Property" : "Edit Property"}
                    </div>
                    <JC_Form
                        key={rerenderGuid}
                        submitButtonText={JC_Utils.stringNullOrEmpty(currentProperty.Id) ? "Create Property" : "Update Property"}
                        onSubmit={handleSubmit}
                        isLoading={isLoading}
                        fields={[
                            // Address
                            {
                                inputId: "property-address-input",
                                type: FieldTypeEnum.Text,
                                label: "Address",
                                value: address,
                                onChange: (newValue) => setAddress(newValue as string),
                                validate: (v: any) => JC_Utils.stringNullOrEmpty(v) ? "Enter property address." : ""
                            },
                            // Building Type
                            {
                                inputId: "building-type-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Building Type",
                                value: buildingTypeCode,
                                onChange: (newValue) => setBuildingTypeCode(newValue as string),
                                options: buildingTypeOptions,
                                enableSearch: true
                            },
                            // Company or Strata Title
                            {
                                inputId: "company-strata-input",
                                type: FieldTypeEnum.Text,
                                label: "Company or Strata Title",
                                value: companyStrataTitle,
                                onChange: (newValue) => setCompanyStrataTitle(newValue as string)
                            },
                            // No. of Bedrooms
                            {
                                inputId: "num-bedrooms-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "No. of Bedrooms",
                                value: numBedroomsCode,
                                onChange: (newValue) => setNumBedroomsCode(newValue as string),
                                options: numBedroomsOptions,
                                enableSearch: true
                            },
                            // Orientation
                            {
                                inputId: "orientation-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Orientation",
                                value: orientationCode,
                                onChange: (newValue) => setOrientationCode(newValue as string),
                                options: orientationOptions,
                                enableSearch: true
                            },
                            // Storeys
                            {
                                inputId: "storeys-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Storeys",
                                value: storeysCode,
                                onChange: (newValue) => setStoreysCode(newValue as string),
                                options: storeysOptions,
                                enableSearch: true
                            },
                            // Furnished
                            {
                                inputId: "furnished-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Furnished",
                                value: furnishedCode,
                                onChange: (newValue) => setFurnishedCode(newValue as string),
                                options: furnishedOptions
                            },
                            // Occupied
                            {
                                inputId: "occupied-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Occupied",
                                value: occupiedCode,
                                onChange: (newValue) => setOccupiedCode(newValue as string),
                                options: occupiedOptions
                            },
                            // Rooms (Multi-select)
                            {
                                inputId: "rooms-input",
                                type: FieldTypeEnum.Custom,
                                label: "Rooms",
                                value: selectedRoomCodes.join(","),
                                customNode: (
                                    <div className={styles.multiSelectContainer}>
                                        <JC_Dropdown
                                            type={DropdownTypeEnum.Multi}
                                            placeholder="Select rooms"
                                            options={roomFieldOptions}
                                            onSelection={handleRoomSelection}
                                        />
                                        {selectedRoomCodes.length > 0 && (
                                            <div className={styles.selectedRooms}>
                                                {selectedRoomNames.map((roomName, index) => (
                                                    <div key={selectedRoomCodes[index]} className={styles.roomChip}>
                                                        {roomName}
                                                        <span
                                                            className={styles.removeButton}
                                                            onClick={() => removeRoom(selectedRoomCodes[index])}
                                                        >
                                                            ×
                                                        </span>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                )
                            }
                        ]}
                    />
                </div>
            </div>

            {/* Right Pane - Property Info */}
            <div className={styles.rightPane}>
                <div className={styles.rightPaneHeader}>
                    Property Information
                </div>
                <div className={styles.contentContainer}>
                    {!JC_Utils.stringNullOrEmpty(address) ? (
                        <div className={styles.propertyInfo}>
                            <div className={styles.infoSection}>
                                <div className={styles.sectionTitle}>Basic Information</div>
                                <div className={styles.infoGrid}>
                                    <div className={styles.infoLabel}>Address:</div>
                                    <div className={styles.infoValue}>{address}</div>

                                    <div className={styles.infoLabel}>Building Type:</div>
                                    <div className={styles.infoValue}>
                                        {buildingTypeOptions.find(o => o.OptionId === buildingTypeCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Company/Strata:</div>
                                    <div className={styles.infoValue}>{companyStrataTitle || "Not specified"}</div>

                                    <div className={styles.infoLabel}>Bedrooms:</div>
                                    <div className={styles.infoValue}>
                                        {numBedroomsOptions.find(o => o.OptionId === numBedroomsCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Orientation:</div>
                                    <div className={styles.infoValue}>
                                        {orientationOptions.find(o => o.OptionId === orientationCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Storeys:</div>
                                    <div className={styles.infoValue}>
                                        {storeysOptions.find(o => o.OptionId === storeysCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Furnished:</div>
                                    <div className={styles.infoValue}>
                                        {furnishedOptions.find(o => o.OptionId === furnishedCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Occupied:</div>
                                    <div className={styles.infoValue}>
                                        {occupiedOptions.find(o => o.OptionId === occupiedCode)?.Label || "Not specified"}
                                    </div>
                                </div>
                            </div>

                            {selectedRoomNames.length > 0 && (
                                <div className={styles.infoSection}>
                                    <div className={styles.sectionTitle}>Selected Rooms</div>
                                    <div className={styles.roomsList}>
                                        {selectedRoomNames.map((roomName, index) => (
                                            <div key={index} className={styles.roomTag}>
                                                {roomName}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    ) : (
                        <div className={styles.noProperty}>
                            Enter property details to see information
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
