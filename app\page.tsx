"use client"

import styles from "./page.module.scss";
import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { JC_Utils } from "./Utils";
import { LocalStorageKeyEnum } from "./enums/LocalStorageKey";

export default function Page_Home() {

    const session = useSession();

    // IF just logged in, show "Welcome"
    useEffect(() => {
        if (localStorage.getItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome) == "1" && session.data != null) {
            JC_Utils.showToastSuccess(`Welcome ${session.data?.user.FirstName}!`);
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "0");
        }
    }, [session.data]);

    return (

        <div className={styles.mainContainer}>

        </div>
    );
}
