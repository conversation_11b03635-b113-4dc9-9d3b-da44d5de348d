"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_DatePicker from "../components/JC_DatePicker/JC_DatePicker";
import { ReportModel } from "../models/Report";
import { PropertyModel } from "../models/Property";
import { UserModel } from "../models/User";
import { FieldTypeEnum } from "../enums/FieldType";
import { JC_Utils, JC_Utils_Rooms } from "../Utils";
import { D_FieldModel_Email, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";

export default function CustomerPage() {
    // - STATE - //
    const { data: session } = useSession();
    const [reports, setReports] = useState<ReportModel[]>([]);
    const [selectedReport, setSelectedReport] = useState<ReportModel | null>(null);
    const [selectedProperty, setSelectedProperty] = useState<PropertyModel | null>(null);
    const [currentUser, setCurrentUser] = useState<UserModel | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [rerenderGuid, setRerenderGuid] = useState<string>(JC_Utils.generateGuid());

    // Form fields
    const [reportType, setReportType] = useState<string>("");
    const [clientName, setClientName] = useState<string>("");
    const [clientPhone, setClientPhone] = useState<string>("");
    const [clientEmail, setClientEmail] = useState<string>("");
    const [clientPrincipalName, setClientPrincipalName] = useState<string>("");
    const [postalAddress, setPostalAddress] = useState<string>("");
    const [inspectionDate, setInspectionDate] = useState<Date>(new Date());
    const [inspectorNameOverride, setInspectorNameOverride] = useState<string>("");
    const [inspectorPhoneOverride, setInspectorPhoneOverride] = useState<string>("");
    const [inspectorQualificationOverride, setInspectorQualificationOverride] = useState<string>("");

    // - EFFECTS - //
    useEffect(() => {
        loadData();
    }, [loadData]);

    useEffect(() => {
        if (selectedReport) {
            populateFormFromReport(selectedReport);
        } else {
            clearForm();
        }
    }, [selectedReport, populateFormFromReport]);

    // - LOAD DATA - //
    const loadData = useCallback(async () => {
        if (!session?.user?.email) return;

        try {
            setIsLoading(true);

            // Load current user
            const userResponse = await fetch(`/api/user?email=${encodeURIComponent(session.user.email)}`);
            if (userResponse.ok) {
                const userData = await userResponse.json();
                setCurrentUser(userData.result);

                // Load reports based on user admin status
                let reportsResponse;
                if (JC_Utils_Rooms.isUserAdmin(userData.result)) {
                    // Admin sees all reports
                    reportsResponse = await fetch('/api/report/getList');
                } else {
                    // Non-admin sees only their reports
                    reportsResponse = await fetch(`/api/report/byUser?userId=${userData.result.Id}`);
                }

                if (reportsResponse.ok) {
                    const reportsData = await reportsResponse.json();
                    setReports(reportsData.result || []);
                }
            }
        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            setIsLoading(false);
        }
    }, [session]);

    // - FORM HANDLING - //
    const populateFormFromReport = useCallback((report: ReportModel) => {
        setReportType(""); // TODO: Add ReportType field to Report model
        setClientName(report.ClientName || "");
        setClientPhone(report.ClientPhone || "");
        setClientEmail(report.ClientEmail || "");
        setClientPrincipalName(report.ClientPrincipalName || "");
        setPostalAddress(report.PostalAddress || "");
        setInspectionDate(report.InspectionDate ? new Date(report.InspectionDate) : new Date());
        setInspectorNameOverride(report.InspectorNameOverride || "");
        setInspectorPhoneOverride(report.InspectorPhoneOverride || "");
        setInspectorQualificationOverride(report.InspectorQualificationOverride || "");

        // Load property for address
        loadProperty(report.PropertyId);
    }, []);

    function clearForm() {
        setReportType("");
        setClientName("");
        setClientPhone("");
        setClientEmail("");
        setClientPrincipalName("");
        setPostalAddress("");
        setInspectionDate(new Date());
        setInspectorNameOverride("");
        setInspectorPhoneOverride("");
        setInspectorQualificationOverride("");
        setSelectedProperty(null);
    }

    async function loadProperty(propertyId: string) {
        try {
            const response = await fetch(`/api/property?id=${propertyId}`);
            if (response.ok) {
                const data = await response.json();
                setSelectedProperty(data.result);
            }
        } catch (error) {
            console.error('Error loading property:', error);
        }
    }

    async function handleSubmit() {
        if (!selectedReport || !currentUser) return;

        try {
            setIsLoading(true);

            const updatedReport = new ReportModel({
                ...selectedReport,
                ClientName: clientName,
                ClientPhone: clientPhone,
                ClientEmail: clientEmail,
                ClientPrincipalName: clientPrincipalName,
                PostalAddress: postalAddress,
                InspectionDate: inspectionDate,
                InspectorNameOverride: inspectorNameOverride,
                InspectorPhoneOverride: inspectorPhoneOverride,
                InspectorQualificationOverride: inspectorQualificationOverride
            });

            const response = await ReportModel.Update(updatedReport);
            if (response) {
                // Refresh the reports list
                await loadData();
                setRerenderGuid(JC_Utils.generateGuid());
            }
        } catch (error) {
            console.error('Error updating report:', error);
        } finally {
            setIsLoading(false);
        }
    }

    // - COMPUTED VALUES - //
    const isUserAdmin = JC_Utils_Rooms.isUserAdmin(currentUser);
    const propertyAddress = selectedProperty?.Address || "";

    // Default inspector values for non-admin users
    const defaultInspectorName = !isUserAdmin && currentUser ?
        `${currentUser.FirstName} ${currentUser.LastName}` : "";
    const defaultInspectorPhone = !isUserAdmin && currentUser ?
        currentUser.Phone || "" : "";
    const defaultInspectorQualification = !isUserAdmin && currentUser ?
        currentUser.Qualification || "" : "";

    // - RENDER - //
    return (
        <div className={styles.mainContainer}>
            {/* Left Pane - Reports List */}
            <div className={styles.leftPane}>
                <div className={styles.leftPaneHeader}>
                    Reports
                </div>
                <div className={styles.reportsList}>
                    {reports.length === 0 ? (
                        <div className={styles.noReports}>
                            {isLoading ? "Loading..." : "No reports found"}
                        </div>
                    ) : (
                        reports.map(report => (
                            <div
                                key={report.Id}
                                className={`${styles.reportItem} ${selectedReport?.Id === report.Id ? styles.selected : ''}`}
                                onClick={() => setSelectedReport(report)}
                            >
                                <div className={styles.reportName}>{report.Name}</div>
                                <div className={styles.reportClient}>{report.ClientName}</div>
                            </div>
                        ))
                    )}
                </div>
            </div>

            {/* Right Pane - Report Form */}
            <div className={styles.rightPane}>
                <div className={styles.rightPaneHeader}>
                    Report Details
                </div>
                {selectedReport ? (
                    <div className={styles.formContainer}>
                        <div className={styles.formTitle}>
                            {selectedReport.Name}
                        </div>
                        <JC_Form
                            key={rerenderGuid}
                            submitButtonText="Update Report"
                            onSubmit={handleSubmit}
                            isLoading={isLoading}
                            fields={[
                                // Report Type
                                {
                                    inputId: "report-type-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Report Type",
                                    value: reportType,
                                    onChange: (newValue) => setReportType(newValue as string),
                                    placeholder: "Enter report type"
                                },
                                // Client Name
                                {
                                    inputId: "client-name-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Client Name",
                                    value: clientName,
                                    onChange: (newValue) => setClientName(newValue as string),
                                    validate: (v: any) => JC_Utils.stringNullOrEmpty(v) ? "Enter client name." : ""
                                },
                                // Client Phone
                                {
                                    ...D_FieldModel_Phone(),
                                    inputId: "client-phone-input",
                                    label: "Client Phone",
                                    value: clientPhone,
                                    onChange: (newValue) => setClientPhone(newValue as string)
                                },
                                // Client Email
                                {
                                    ...D_FieldModel_Email(),
                                    inputId: "client-email-input",
                                    label: "Client Email",
                                    value: clientEmail,
                                    onChange: (newValue) => setClientEmail(newValue as string)
                                },
                                // Client Principal Name
                                {
                                    inputId: "client-principal-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Client Principal Name",
                                    value: clientPrincipalName,
                                    onChange: (newValue) => setClientPrincipalName(newValue as string)
                                },
                                // Property Address (read-only)
                                {
                                    inputId: "property-address-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Property Address",
                                    value: propertyAddress,
                                    readOnly: true
                                },
                                // Postal Address
                                {
                                    inputId: "postal-address-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Postal Address",
                                    value: postalAddress,
                                    onChange: (newValue) => setPostalAddress(newValue as string)
                                },
                                // Inspection Date (Custom field with date picker)
                                {
                                    inputId: "inspection-date-input",
                                    type: FieldTypeEnum.Custom,
                                    label: "Inspection Date",
                                    value: inspectionDate.toISOString(),
                                    customNode: (
                                        <JC_DatePicker
                                            inputId="inspection-date-picker"
                                            theDate={inspectionDate}
                                            onChange={(newDate) => setInspectionDate(newDate)}
                                        />
                                    )
                                },
                                // Inspector Name Override
                                {
                                    inputId: "inspector-name-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Inspector Name Override",
                                    value: inspectorNameOverride || defaultInspectorName,
                                    onChange: (newValue) => setInspectorNameOverride(newValue as string),
                                    readOnly: !isUserAdmin,
                                    placeholder: !isUserAdmin ? defaultInspectorName : "Enter inspector name"
                                },
                                // Inspector Phone Override
                                {
                                    ...D_FieldModel_Phone(),
                                    inputId: "inspector-phone-input",
                                    label: "Inspector Phone Override",
                                    value: inspectorPhoneOverride || defaultInspectorPhone,
                                    onChange: (newValue) => setInspectorPhoneOverride(newValue as string),
                                    readOnly: !isUserAdmin,
                                    placeholder: !isUserAdmin ? defaultInspectorPhone : "Enter inspector phone"
                                },
                                // Inspector Qualification Override
                                {
                                    inputId: "inspector-qualification-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Inspector Qualification Override",
                                    value: inspectorQualificationOverride || defaultInspectorQualification,
                                    onChange: (newValue) => setInspectorQualificationOverride(newValue as string),
                                    readOnly: !isUserAdmin,
                                    placeholder: !isUserAdmin ? defaultInspectorQualification : "Enter inspector qualification"
                                }
                            ]}
                        />
                    </div>
                ) : (
                    <div className={styles.noSelection}>
                        Select a report to view details
                    </div>
                )}
            </div>
        </div>
    );
}
