@import '../../global';

.table {
    margin-top: 14px;
    width: auto;
    border-collapse: collapse;
    color: $white;

    th, td {
        padding: 12px 30px;
        text-align: center;
        border-bottom: $smallBorderWidth solid rgba($primaryColor, 0.3);
        white-space: normal;
        word-wrap: break-word;
        max-width: 300px; /* Limit width to force wrapping */
    }

    th {
        color: $primaryColor;
        font-weight: bold;
        background-color: transparent;
        text-align: center;
        cursor: pointer;
        position: relative;
        user-select: none;
        white-space: normal;
        width: auto;
        word-wrap: break-word;

        /* Sortable headers are centered by default */

        .headerLabelContainer {
            position: relative;
            width: max-content;
            margin: 0 auto;
        }

        .headerLabel {
            width: max-content;
            height: max-content;
            display: inline-block;
            text-align: center;
        }

        .sortIndicator {
            display: inline-block;
            position: absolute;
            width: 13px;
            height: auto;
            right: -20px;
            top: 41%;
            transform: translateY(-50%);
            z-index: 1; /* Ensure it's above other elements */
            filter: invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
        }
    }

    thead tr:hover {
        background-color: transparent;
    }

    td {
        text-align: center;
    }

    tbody tr {
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: rgba($primaryColor, 0.1);
        }
    }

    tbody .tableRow {
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: rgba($primaryColor, 0.1);
        }
    }

    .noDataRow {
        cursor: default;

        &:hover {
            background-color: transparent;
        }

        td {
            border-bottom: none;
        }
    }

    .noDataCell {
        color: $white;
        text-align: center;
        padding-top: 20px;
        font-weight: bold;
    }
}

.spinnerContainer {
    height: 44px;
}

// Responsive column hiding
@media (max-width: $largeScreenSize) {
    .hideOnLarge {
        display: none !important;
    }
}

@media (max-width: $mediumScreenSize) {
    .hideOnMedium {
        display: none !important;
    }
}

@media (max-width: $smallScreenSize) {
    .hideOnSmall {
        display: none !important;
    }
}

@media (max-width: $tinyScreenSize) {
    .hideOnTiny {
        display: none !important;
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .hideOnTeenyTiny {
        display: none !important;
    }
}

.buttonContainer {
    min-width: 140px;
    width: max-content;
    height: 44px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: $offWhite;
    outline-color: $primaryColor;
    outline-width: $smallBorderWidth;
    outline-style: solid;
    border-radius: $largeBorderRadius;
    font-size: 20px;
    font-weight: bold;
    overflow: visible;
    user-select: none;
    cursor: pointer;
    transition: outline-width  0.15s,
                outline-color  0.35s,
                outline-offset 0.15s;

    // Text
    .buttonText {
        width: max-content;
        overflow: visible;
        color: black;
    }

    // Hover
    &:hover {
        outline-width: $largeBorderWidth;
        outline-color: $secondaryColor;
        outline-offset: -1px;
        transition: outline-width  0.04s,
                    outline-color  0.08s,
                    outline-offset 0.04s;
    }

    // Selected
    &.buttonSelected {
        outline-width: $largeBorderWidth;
        outline-color: $secondaryColor;
        outline-offset: -1px;
        cursor: default !important;
        transition: outline-width 0s,
                    outline-color 0s;
    }

    // Icon
    &.includeIcon {
        column-gap: 8px;
        justify-content: space-between;
        .buttonIcon {
            width: 20px;
            height: auto;
            margin-left: -5px;
        }
        .buttonText {
            position: static;
            transform: translate(0, 0);
            flex-grow: 1;
        }
    }

    // Icon Only
    &.iconOnly {
        min-width: 0;
        padding: 0 15px;
        border-radius: $tinyBorderRadius;
        .buttonIcon {
            margin-left: 0;
        }
    }

    // Icon On Top
    &.iconOnTop {
        flex-direction: column;
        padding: 5px 0;
        height: max-content;
        min-height: 82px;
        row-gap: 5px;
        justify-content: space-between;
        .buttonText {
            flex-grow: 0;
        }
        .buttonIcon {
            margin-top: 2px;
            height: 40px;
            width: auto;
            margin-left: 0;
        }
    }

    // Secondary
    &.secondary {
        outline-color: transparent;
        background-color: $primaryColor;
        &:hover { outline-color: $secondaryColor; }
        &.buttonSelected { outline-color: $secondaryColor !important; }
    }

    // Small
    &.small {
        min-width: 60px;
        height: 34px;
        padding: 0 14px;
        font-size: 14px;
        // font-weight: normal;
        &:hover { outline-width: $smallBorderWidth; }
        &.buttonSelected { outline-width: $smallBorderWidth }
    }

    // Disabled
    &.disabled {
        outline: none !important;
        // background-color: $greyHover;
        opacity: 0.7;
        cursor: inherit;
        &:hover {
            outline-width: $smallBorderWidth;
            outline-color: $primaryColor;
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $tinyScreenSize) {
    .enableResponsive.buttonContainer {
        min-width: 115px;
        height: 36px;
        font-size: 16px;

        .buttonIcon {
            width: 17px !important;
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .enableResponsive.buttonContainer {
        min-width: 95px;
        height: 30px;
        padding: 0 10px;
        font-size: 13px;
        outline-width: $tinyBorderWidth;

        &:hover {
            outline-width: $smallBorderWidth;
        }
        &.buttonSelected {
            outline-width: $smallBorderWidth;
        }
        .buttonIcon {
            width: 13px !important;
            margin-left: 0 !important;
            margin-right: -2px !important;
        }
    }
}