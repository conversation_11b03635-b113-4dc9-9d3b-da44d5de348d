import styles from "./JC_Header.module.scss";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import { auth } from "@/app/auth";

export default async function JC_Header() {

    const session = useSession();
    const pathName = usePathname();
    let loggedIn:boolean = !!session?.data?.user;

    // Helper function to check if we're on a specific page (same logic as J<PERSON>_<PERSON><PERSON>)
    const isOnPage = (page: string) => {
        return pathName == page || pathName == `/${page}`;
    };

    return (
        <React.Fragment>

            {/* Header */}
            <div className={styles.mainContainer} id="JC_header">

                {/* Logo + Account */}
                <div className={styles.logoAccountContainer}>

                    {/* Logo */}
                    <Image
                        src="/logos/Main [Simple].webp"
                        alt={"MainLogo"}
                        width={0}
                        height={0}
                        className={styles.logo}
                        unoptimized
                    />

                    {/* Account */}
                    {loggedIn && (
                        <div className={`${styles.checkoutAccountContainer}`}>
                            <Link href="/account" className={styles.navButton}>
                                <div className={styles.navButtonContent}>
                                    <Image
                                        src="/icons/User2.webp"
                                        alt="Account"
                                        width={0}
                                        height={0}
                                        className={styles.navIcon}
                                        unoptimized
                                    />
                                    <span className={styles.navLabel}>{session?.data?.user.FirstName}</span>
                                </div>
                            </Link>
                        </div>
                    )}

                </div>

                {/* Navs */}
                <div className={styles.navsContainer}>

                    {/* Nav Buttons */}
                    <div className={styles.navButtons}>
                        <Link
                            href="/customer"
                            className={`${styles.navButton} ${isOnPage('customer') ? styles.active : ''}`}
                        >
                            <div className={styles.navButtonContent}>
                                <Image
                                    src="/icons/User2.webp"
                                    alt="Customer"
                                    width={0}
                                    height={0}
                                    className={styles.navIcon}
                                    unoptimized
                                />
                                <span className={styles.navLabel}>Customer</span>
                            </div>
                        </Link>

                        <Link
                            href="/property"
                            className={`${styles.navButton} ${isOnPage('property') ? styles.active : ''}`}
                        >
                            <div className={styles.navButtonContent}>
                                <Image
                                    src="/icons/User2.webp"
                                    alt="Property"
                                    width={0}
                                    height={0}
                                    className={styles.navIcon}
                                    unoptimized
                                />
                                <span className={styles.navLabel}>Property</span>
                            </div>
                        </Link>

                        <Link
                            href="/defects"
                            className={`${styles.navButton} ${isOnPage('defects') ? styles.active : ''}`}
                        >
                            <div className={styles.navButtonContent}>
                                <Image
                                    src="/icons/User2.webp"
                                    alt="Defects"
                                    width={0}
                                    height={0}
                                    className={styles.navIcon}
                                    unoptimized
                                />
                                <span className={styles.navLabel}>Defects</span>
                            </div>
                        </Link>
                    </div>

                </div>

            </div>
        </React.Fragment>
    );
}
