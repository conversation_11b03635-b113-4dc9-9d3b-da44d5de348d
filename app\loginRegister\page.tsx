"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Tabs from "../components/JC_Tabs/JC_Tabs";
import JC_Checkbox from "../components/JC_Checkbox/JC_Checkbox";
import JC_PasswordRequirements from "../components/JC_PasswordRequirements/JC_PasswordRequirements";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Post } from "../apiServices/JC_Post";


import { signIn, useSession } from "next-auth/react";
import { authenticate } from "../actions";
import { UserModel } from "../models/User";
import { D_FieldModel_Email, D_FieldModel_FirstName, D_FieldModel_LastName, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";
import { FieldTypeEnum } from "../enums/FieldType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { JC_Utils, JC_Utils_Validation } from "../Utils";


export default function Page_LoginRegister() {

    const session = useSession();
    const router = useRouter();

    // - STATE - //

    // Loading
    const [isLoading, setIsLoading] = useState<boolean>(false);
    // Error
    const [errorMessage, setErrorMessage] = useState<string>();
    // Form validation
    const [submitClicked, setSubmitClicked] = useState<boolean>(false);
    // Login
    const [loginEmail, setLoginEmail] = useState<string>("");
    const [loginPassword, setLoginPassword] = useState<string>("");
    // Register
    const [registerFirstName, setRegisterFirstName] = useState<string>("");
    const [registerLastName, setRegisterLastName] = useState<string>("");
    const [registerEmail, setRegisterEmail] = useState<string>("");
    const [registerPhone, setRegisterPhone] = useState<string>();
    const [registerCompany, setRegisterCompany] = useState<string>("");
    const [registerPassword, setRegisterPassword] = useState<string>("");
    const [registerConfirmPassword, setRegisterConfirmPassword] = useState<string>("");
    const [emailPromotionsChecked, setEmailPromotionsChecked] = useState<boolean>(true);


    // - INITIALISE - //

    useEffect(() => {
        if (!JC_Utils.isOnMobile()) {
            (document.getElementById("login-register-first-input") as HTMLInputElement).select();
        }
        if (localStorage.getItem(LocalStorageKeyEnum.JC_ShowForgotPasswordSent) == "1") {
            JC_Utils.showToastSuccess("A password reset link has been sent to your email!");
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowForgotPasswordSent, "0");
        }
    }, []);


    // - HANDLES - //

    async function login() {
        setIsLoading(true);
        setErrorMessage("");
        // Login, then go back "Home"
        localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "1");
        let result = await authenticate(loginEmail, loginPassword);
        // IF error
        if (result.error) {
            setErrorMessage(result.error);
            setIsLoading(false);
        // ELSE sign in again so session updates properly then it takes User to Home
        } else {
            await signIn("credentials", { email: loginEmail, password: loginPassword, callbackUrl: "/" });
        }
    }

    async function register() {
        setSubmitClicked(true);
        setIsLoading(true);
        setErrorMessage("");
        try {
            let newUser:UserModel = new UserModel({
                FirstName: registerFirstName,
                LastName: registerLastName,
                Email: registerEmail,
                Phone: registerPhone,
                CompanyName: !JC_Utils.stringNullOrEmpty(registerCompany) ? registerCompany : undefined,
                IsEmailSubscribed: emailPromotionsChecked
            });
            // Create User
            await JC_Put<{ userData:UserModel, password:string }>(
                UserModel.apiRoute,
                { userData: newUser, password: registerPassword }
            );
            // Send 'Welcome' email
            JC_Post("email/welcomeEmail", { name: `${newUser.FirstName} ${newUser.LastName}`, email: newUser.Email });
            // Login, then go back "Home"
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "1");
            await signIn("credentials", { email: registerEmail, password: registerPassword, callbackUrl: "/" });
        } catch (error) {
            setErrorMessage((error as {message:string}).message);
            setIsLoading(false);
        }
    }


    // - Main - //

    return (
        <div className={styles.mainContainer}>
            <JC_Tabs
                onTabChange={() => {
                    setLoginPassword("");
                    setRegisterPassword("");
                    setRegisterConfirmPassword("");
                    setErrorMessage("");
                    setSubmitClicked(false);
                    if (!JC_Utils.isOnMobile()) {
                        setTimeout(() => (document.getElementById("login-register-first-input") as HTMLInputElement).select(), 0);
                    }
                }}
                tabs={[{
                    title: "Login",
                    body: <div className={styles.tabBody}>

                        {/* Form */}
                        <JC_Form
                            submitButtonText="Login"
                            onSubmit={login}
                            isLoading={isLoading}
                            errorMessage={errorMessage}
                            fields={[
                                // Email
                                {
                                    ...D_FieldModel_Email(),
                                    inputId:"login-register-first-input",
                                    onChange: (newValue) => setLoginEmail(newValue),
                                    value: loginEmail
                                },
                                // Password
                                {
                                    inputId: "login-password-input",
                                    type: FieldTypeEnum.Password,
                                    label: "Password",
                                    onChange: (newValue) => setLoginPassword(newValue),
                                    value: loginPassword,
                                    validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a password." : ""
                                }
                            ]}
                        />

                        {/* Forgot Password */}
                        <div className={styles.smallTextButton} onClick={() => router.push("forgotPassword")}>
                            Forgot Password?
                        </div>

                    </div>
                },{
                    title: "Register",
                    body: <div className={styles.tabBody}>

                        {/* Form */}
                        <JC_Form
                            key={errorMessage}
                            submitButtonText="Register"
                            onSubmit={register}
                            isLoading={isLoading}
                            errorMessage={errorMessage}
                            fields={[
                                // First Name
                                {
                                    ...D_FieldModel_FirstName(),
                                    inputId:"login-register-first-input",
                                    onChange: (newValue) => setRegisterFirstName(newValue),
                                    value: registerFirstName
                                },
                                // Last Name
                                {
                                    ...D_FieldModel_LastName(),
                                    inputId:"register-last-name-input",
                                    onChange: (newValue) => setRegisterLastName(newValue),
                                    value: registerLastName
                                },
                                // Email
                                {
                                    ...D_FieldModel_Email(),
                                    inputId:"register-email-input",
                                    onChange: (newValue) => setRegisterEmail(newValue),
                                    value: registerEmail
                                },
                                // Phone
                                {
                                    ...D_FieldModel_Phone(),
                                    inputId:"register-phone-input",
                                    onChange: (newValue) => setRegisterPhone(newValue),
                                    value: registerPhone,
                                },
                                // Company Name
                                {
                                    inputId: "register-company-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Company (optional)",
                                    iconName: "User",
                                    value: registerCompany,
                                    onChange: (newValue) => setRegisterCompany(newValue)
                                },
                                // Password
                                {
                                    inputId:"register-password-input",
                                    type: FieldTypeEnum.Password,
                                    label: "Password",
                                    onChange: (newValue) => setRegisterPassword(newValue),
                                    value: registerPassword,
                                    validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                                            ? "Enter a password."
                                                            : !JC_Utils_Validation.validPassword(v)
                                                                ? `Password invalid.`
                                                                : ""
                                },
                                // Password Requirements
                                {
                                    overrideClass: styles.passwordRequirementsField,
                                    inputId: "password-requirements",
                                    type: FieldTypeEnum.Text,
                                    customNode: <JC_PasswordRequirements
                                                    key="password-requirements"
                                                    password={registerPassword}
                                                    showErrors={submitClicked}
                                                />
                                },
                                // Confirm Password
                                {
                                    inputId:"register-confirm-password-input",
                                    type: FieldTypeEnum.Password,
                                    label: "Confirm Password",
                                    onChange: (newValue) => setRegisterConfirmPassword(newValue),
                                    value: registerConfirmPassword,
                                    validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                                            ? "Confirm the password."
                                                            : registerConfirmPassword != registerPassword
                                                                ? "Passwords do not match"
                                                                : ""
                                },
                                // Email Promotions
                                {
                                    overrideClass: styles.fieldOverride,
                                    inputId: "email-promotions-checkbox",
                                    type: FieldTypeEnum.Text,
                                    customNode: <JC_Checkbox key="details-&-payment-title" label="Email Promotions" checked={emailPromotionsChecked} onChange={() => setEmailPromotionsChecked(!emailPromotionsChecked)} />
                                }
                            ]}
                        />





                    </div>
                }]}
            />
        </div>
    );
}
