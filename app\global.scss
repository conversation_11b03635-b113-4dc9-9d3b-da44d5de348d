
// - VARIABLES - //

// Colours
$primaryColor: #031363;
$secondaryColor: #539fc1;
$miscellaneousColor1: #F0C31F;
$miscellaneousColor2: #2DB737;
$white: #FFFFFF;
$offWhite: #F8F8F8;
$black: #000000;
$offBlack: #303030;
$darkGrey: #4e4e4e;
$lightGrey: #ececec;
$greyHover: #dedede;
$errorColor: #be3d3d;
$successColor: #4caf50;

// Screen Sizes
$teenyTinyScreenSize: 600px;
$tinyScreenSize: 790px;
$smallScreenSize: 1020px;
$mediumScreenSize: 1360px;
$largeScreenSize: 1500px;

// Page
$bodyLeftRightPadding: 30px;
$bodyTopBottomPadding: 60px;
$pageMaxWidth: 1400px;
$emptyPageHeight: 500px;
@mixin mainPageStyles {
    margin: auto;
    max-width: $pageMaxWidth;
    min-height: 400px;
    padding: $bodyTopBottomPadding $bodyLeftRightPadding;
}

// Borders
$tinyBorderWidth: 2px;
$smallBorderWidth: 3px;
$largeBorderWidth: 4px;
$tinyBorderRadius: 5px;
$smallBorderRadius: 20px;
$largeBorderRadius: 30px;

// Font
$smallFontSize: 11px;
$defaultFontSize: 13px;
$titleFontSize: 56px;
@mixin labelSmall {
    font-size: 11px;
    font-weight: bold;
}
@mixin labelLarge {
    font-size: 13px;
    font-weight: bold;
}
@mixin textOutline($width, $color) {
    text-shadow: (-$width) (-$width) 0 $color,
                   $width  (-$width) 0 $color,
                 (-$width)   $width  0 $color,
                   $width    $width  0 $color;
}

// Images
$smallImageWidth: 80px;
$smallImageHeight: 62px;
$charlotteOpacity: 0.85;

// Dropdowns
@mixin dropdownIcon {
    width: 15%;
    height: auto;
    max-height: 60%;
}
@mixin dropdownOptionContainer {
    position: relative;
    width: 100%;
    height: 40px;
    padding: 0 38px 0 10px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-content: flex-start;
    align-items: center;
    column-gap: 10px;
    border-radius: 6px;
    border: solid $tinyBorderWidth $secondaryColor;
    background-color: $offWhite;
    overflow: hidden;

    .optionLabel {
        flex-grow: 1;
        font-size: 13px;
        text-align: left !important;
    }

    .checkbox {
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 10px;
    }

}
@mixin dropdownDropdown {
    position: absolute;
    bottom: -4px; transform: translateY(100%);
    width: 100%;
    max-height: 375px;
    overflow-y: auto;
    box-sizing: border-box;
    border-radius: 6px;
    background: rgba(#d3d3d3, 1);
    z-index: 80;
    animation: extendHeightAnimation 0.2s ease-in-out;

    .dropdownOption {
        @include dropdownOptionContainer;
        margin-bottom: 4px;
        border-color: $primaryColor;
        cursor: pointer;
        &.selected {
            background-color: $greyHover;
            cursor: default;
        }
        &:hover { background-color: $greyHover; }
        &:last-child { margin-bottom: 0; }
    }
}
@keyframes extendHeightAnimation {
    0%   {  max-height: 0;     overflow: hidden; }
    100% {  max-height: 375px; overflow: hidden; }
}

// Outside Click Div
@mixin outsideClickDiv {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: $offBlack;
    z-index: 90;
    opacity: 0.15;
    transition: opacity 0.2s ease-out;
}

// Container Shadow
@mixin containerShadow {
    box-shadow: 0 0 8px 2px rgba($black, 0.85);
}

// Hide Scrollbar
@mixin hideScrollBar {
    &::-webkit-scrollbar {
        display: none;
    }
    & {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
}



// - GLOBAL CLASSES - //

.forceWhiteBackground {
    background-color: white !important;
}
.forceOverflowHidden {
    overflow: hidden !important;
}
.forceFullScreen {
    min-width: 100vw;
    min-height: 100vh;
}
.forceHidden {
    display: none !important;
}
.forceOverflowYHidden {
    overflow-y: hidden !important;
}


 // - EXPORT - //

:export {
    primaryColor: $primaryColor;
    secondaryColor: $secondaryColor;
    offWhite: $offWhite;
    offBlack: $offBlack;
    errorColor: $errorColor;
    successColor: $successColor;
}