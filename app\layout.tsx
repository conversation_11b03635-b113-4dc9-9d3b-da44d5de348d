import styles from "./layout.module.scss";
import type { Metadata } from "next";
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Inter, Special_Elite, Margarine } from 'next/font/google';
import { ToastContainer } from "react-toastify";
import { SessionProvider } from "next-auth/react";
import J<PERSON>_Header from "./components/JC_Header/JC_Header";
import { auth } from "./auth";
import TempComingSoon from "./tempComingSoon";
import { headers } from 'next/headers';
import { shouldHideHeaderFooter } from './utils/pageConfig';

// Site Metadata
export const metadata: Metadata = {
    title: process.env.NAME,
    description: "Building Inspection site for AIMS Engineering.",
    robots: {
        index: false,
        follow: false,
        googleBot: {
            index: false,
            follow: false,
        },
    },
};

// Font
const inter = Inter({ subsets: ["latin"], variable: '--font-inter' });
const kaushanScript = Special_Elite({ weight: "400", subsets: ["latin"], variable: '--font-kaushan-script' });
const shadowsIntoLight = Margarine({ weight: "400", subsets: ["latin"], variable: '--title-font' });

// Site Root
export default async function Layout_Root(_: Readonly<{
    children: React.ReactNode;
}>) {
    // Get the current path to check if it's a demo page and if header/footer should be hidden
    const headersList = headers();
    const path = headersList.get('x-pathname') || headersList.get('x-invoke-path') || '';
    const isDemoPage = path.startsWith('/demo');
    const hideHeaderFooter = shouldHideHeaderFooter(path);

    const session = await auth();

    // Allow access to demo pages without authentication
    const showContent = true; // session || isDemoPage;

    return (
        <html lang="en">

            <body className={`${styles.rootMainContainer} ${inter.variable} ${kaushanScript.variable} ${shadowsIntoLight.variable}`} id="rootMainContainer">

                <div className={styles.mainLayout}>
                    {showContent && !hideHeaderFooter && <JC_Header />}

                    {showContent && <div className={styles.pageContainer}>
                        <SessionProvider session={session}>
                            {_.children}
                        </SessionProvider>
                    </div>}
                </div>

                {!showContent && <TempComingSoon />}

                <ToastContainer />

                <SpeedInsights />

            </body>

        </html>
  );
}
