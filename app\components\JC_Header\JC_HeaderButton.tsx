"use client"

import styles from "./JC_Header.module.scss";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface JC_HeaderButtonProps {
    linkToPage: string;
    text: string;
    iconName: string;
}

export default function JC_HeaderButton({ linkToPage, text, iconName }: JC_HeaderButtonProps) {
    const pathName = usePathname();

    // Helper function to check if we're on a specific page (same logic as JC_Button)
    const isOnPage = (page: string) => {
        return pathName == page || pathName == `/${page}`;
    };

    return (
        <Link
            href={`/${linkToPage}`}
            className={`${styles.navButton} ${isOnPage(linkToPage) ? styles.active : ''}`}
        >
            <div className={styles.navButtonContent}>
                <Image
                    src={`/icons/${iconName}.webp`}
                    alt={text}
                    width={0}
                    height={0}
                    className={styles.navIcon}
                    unoptimized
                />
                <span className={styles.navLabel}>{text}</span>
            </div>
        </Link>
    );
}
