@import 'global';

.mainContainer {
    padding-top: 0;
    width: 100%;
    height: 100%;
    flex-grow: 1;
    min-height: 400px;

    // Important Info
    .importantInfoContainer {
        position: relative;
        height: 100%;

        // Background Image Slideshow
        .impInfoBackgroundImage, .darkenImage {
            width: 100%;
        }
        .impInfoBackgroundImage {
            height: 100%;
            object-fit: cover;
        }

        // Text
        .impInfoText {
            z-index: 99;
            position: absolute;
            left: 50%; top: 50%; transform: translate(-50.5%, -50%);
            width: max-content;
            height: max-content;
            text-align: center;
            border-radius: $largeBorderRadius;
            padding: 60px;
            box-sizing: border-box;
            background-color: rgba($offBlack, 0.5);
            text-align: center;
            font-size: 56px;
            letter-spacing: 2px;
            word-spacing: 5px;
            line-height: 150%;
            font-family: var(--title-font);
            color: $offWhite;
            user-select: none;
            transition: background-color 0.2s ease-out,
                        transform 0.2s ease-out;
        }
        

        &:hover {
            .impInfoText {
                background-color: rgba($offBlack, 0.65);
                transform: translate(-50%, -50%) scale(103%);
            }
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .importantInfoContainer {
        .impInfoText {
            padding: 40px !important;
            font-size: 46px !important;
        }
    }
}

@media (max-width: $smallScreenSize) {
    .importantInfoContainer {
        .impInfoText {
            font-size: 36px !important;
        }
    }
}

@media (max-width: $tinyScreenSize) {
    .importantInfoContainer {
        .impInfoText {
            padding: 30px !important;
            font-size: 24px !important;
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .importantInfoContainer {
        .impInfoText {
            border-radius: $smallBorderRadius !important;
            padding: 16px !important;
            font-size: 16px !important;
        }
    }
}