@import '../../global';

$imageWidth: 70px;

.spinnerContainer {
    width: 100%;
    height: 100%;
    position: relative;
    user-select: none;

    &.small {
        transform: scale(0.4);
    }
}

.spinnerImage {
    margin-top: 5px;
    height: auto;
    position: absolute;
    left: 50%; top: 50%;
    animation: fadeInAnimation  0.4s          ease-out,
               spinAnimation    1.3s infinite linear,
               pulsingAnimation 0.9s infinite ease-in-out;
}

/* Pulse In */

@keyframes fadeInAnimation {
    0%   {  opacity: 0; }
    100% {  opacity: 1; }
}

/* Spin */

@keyframes spinAnimation {
    0%   { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
} 

/* Pulse */

@keyframes pulsingAnimation {
    0%   {  width: $imageWidth; }
    60%  {  width: calc($imageWidth + 15px); }
    100% {  width: $imageWidth; }
}